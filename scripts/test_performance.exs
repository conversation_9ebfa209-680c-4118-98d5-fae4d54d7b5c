#!/usr/bin/env elixir

# Test performance measurement script
# This script measures test execution time to validate async improvements

defmodule TestPerformance do
  def measure_test_time(test_pattern) do
    {time_microseconds, _result} = :timer.tc(fn ->
      System.cmd("mix", ["test", test_pattern], stderr_to_stdout: true)
    end)
    
    time_seconds = time_microseconds / 1_000_000
    IO.puts("Test pattern '#{test_pattern}' took #{Float.round(time_seconds, 2)} seconds")
    time_seconds
  end

  def run_performance_test do
    IO.puts("=== Test Performance Measurement ===")
    IO.puts("Measuring execution time for different test patterns...")
    IO.puts("")

    # Test a subset of async tests
    async_tests = [
      "test/drops/relation/metadata_test.exs",
      "test/drops/relation/behavioral_test.exs", 
      "test/drops/sql/database/table_test.exs",
      "test/drops/relation/compilers/sqlite_schema_compiler_test.exs",
      "test/drops/relation/compilers/postgres_schema_compiler_test.exs"
    ]

    IO.puts("Testing async-enabled tests...")
    async_time = measure_test_time(Enum.join(async_tests, " "))
    
    IO.puts("")
    IO.puts("=== Results ===")
    IO.puts("Async tests: #{Float.round(async_time, 2)} seconds")
    IO.puts("")
    IO.puts("Note: These tests are now running with async: true")
    IO.puts("Performance improvement depends on CPU cores and test complexity.")
  end
end

TestPerformance.run_performance_test()
