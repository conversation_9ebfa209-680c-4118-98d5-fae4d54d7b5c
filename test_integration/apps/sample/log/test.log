[31m
13:09:57.159 [error] Exqlite.Connection (#PID<0.820.0>) failed to connect: ** (Exqlite.Error) database is locked
[0m
11:18:06.056 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:18:06.067 [debug] QUERY OK db=0.4ms decode=0.8ms idle=3.0ms
PRAGMA foreign_key_list(users) []

11:18:06.067 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA index_list(users) []

11:18:06.068 [debug] QUERY OK db=0.0ms idle=5.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:18:06.068 [debug] QUERY OK db=0.0ms idle=6.8ms
PRAGMA table_info(users) []

11:18:06.857 [debug] QUERY OK db=0.6ms decode=1.2ms queue=1.7ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:07.326 [debug] QUERY OK db=0.7ms decode=0.7ms queue=2.3ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:07.778 [debug] QUERY OK db=0.7ms decode=0.7ms queue=2.4ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:08.371 [debug] QUERY OK db=0.5ms decode=0.8ms idle=35.9ms
PRAGMA foreign_key_list(users) []

11:18:08.372 [debug] QUERY OK db=0.0ms idle=40.1ms
PRAGMA index_list(users) []

11:18:08.374 [debug] QUERY OK db=0.0ms idle=40.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:18:08.374 [debug] QUERY OK db=0.0ms idle=41.5ms
PRAGMA table_info(users) []

11:18:08.884 [debug] QUERY OK db=0.5ms decode=1.0ms queue=2.6ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:09.994 [debug] QUERY OK db=0.6ms decode=1.0ms idle=40.1ms
PRAGMA foreign_key_list(users) []

11:18:09.995 [debug] QUERY OK db=0.0ms idle=44.3ms
PRAGMA index_list(users) []

11:18:09.996 [debug] QUERY OK db=0.1ms idle=44.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:18:09.996 [debug] QUERY OK db=0.0ms idle=45.6ms
PRAGMA table_info(users) []

11:18:18.598 [info] Refreshing Drops.Relation cache...

11:18:18.605 [info] Repositories: [Sample.Repos.Sqlite]

11:18:18.605 [info] Tables: all

11:18:18.605 [info] Processing repository: Sample.Repos.Sqlite

11:18:18.614 [info]   Cache cleared for Sample.Repos.Sqlite

11:18:18.621 [debug] QUERY OK db=0.5ms decode=0.7ms idle=4.1ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:18.621 [info]   No tables found in Sample.Repos.Sqlite

11:18:18.621 [info]   Cache warmed up for 0 tables

11:18:18.621 [info] Successful: 1

11:18:18.621 [info] Failed: 0

11:18:19.294 [info] Refreshing Drops.Relation cache...

11:18:19.297 [info] Repositories: [Sample.Repos.Sqlite]

11:18:19.299 [info] Tables: ["users", "posts"]

11:18:19.299 [info] Processing repository: Sample.Repos.Sqlite

11:18:19.299 [info]   Cache cleared for Sample.Repos.Sqlite

11:18:19.300 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:18:19.312 [debug] QUERY OK db=0.6ms decode=1.0ms idle=4.4ms
PRAGMA foreign_key_list(users) []

11:18:19.312 [debug] QUERY OK db=0.1ms idle=6.8ms
PRAGMA index_list(users) []

11:18:19.312 [debug] QUERY OK db=0.0ms idle=5.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:18:19.312 [debug] QUERY OK db=0.0ms idle=5.9ms
PRAGMA table_info(users) []

11:18:19.326 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:18:19.326 [debug] QUERY OK db=0.0ms idle=19.5ms
PRAGMA foreign_key_list(posts) []

11:18:19.326 [debug] QUERY OK db=0.0ms idle=19.5ms
PRAGMA index_list(posts) []

11:18:19.326 [debug] QUERY OK db=0.0ms idle=19.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:18:19.326 [debug] QUERY OK db=0.0ms idle=19.6ms
PRAGMA table_info(posts) []

11:18:19.327 [info]   Cache warmed up for 2 tables

11:18:19.327 [info] Successful: 1

11:18:19.327 [info] Failed: 0

11:18:20.045 [debug] QUERY OK db=0.7ms decode=1.0ms idle=1.2ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:21.061 [info] Refreshing Drops.Relation cache...

11:18:21.064 [info] Repositories: [Sample.Repos.Sqlite]

11:18:21.064 [info] Tables: all

11:18:21.064 [info] Processing repository: Sample.Repos.Sqlite

11:18:21.064 [info]   Cache cleared for Sample.Repos.Sqlite

11:18:21.075 [debug] QUERY OK db=0.6ms decode=1.0ms idle=1.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:21.075 [info]   No tables found in Sample.Repos.Sqlite

11:18:21.075 [info]   Cache warmed up for 0 tables

11:18:21.075 [info] Successful: 1

11:18:21.075 [info] Failed: 0

11:18:21.507 [info] Refreshing Drops.Relation cache...

11:18:21.514 [info] Repositories: [Sample.Repos.Sqlite]

11:18:21.516 [info] Tables: ["non_existent_table"]

11:18:21.516 [info] Processing repository: Sample.Repos.Sqlite

11:18:21.524 [info]   Cache cleared for Sample.Repos.Sqlite

11:18:21.525 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.non_existent_table (not cached: {:file_read, :enoent})

11:18:21.534 [debug] QUERY OK db=0.7ms decode=1.1ms idle=12.1ms
PRAGMA foreign_key_list(non_existent_table) []

11:18:21.534 [debug] QUERY OK db=0.1ms queue=0.1ms idle=14.9ms
PRAGMA index_list(non_existent_table) []

11:18:21.534 [debug] QUERY OK db=0.0ms idle=15.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["non_existent_table"]

11:18:21.534 [debug] QUERY OK db=0.0ms idle=15.2ms
PRAGMA table_info(non_existent_table) []

11:18:21.546 [info]   Cache warmed up for 1 tables

11:18:21.548 [info] Successful: 1

11:18:21.548 [info] Failed: 0

11:18:21.971 [info] Refreshing Drops.Relation cache...

11:18:21.979 [info] Repositories: [Sample.Repos.Sqlite]

11:18:21.979 [info] Tables: all

11:18:21.979 [info] Processing repository: Sample.Repos.Sqlite

11:18:21.987 [info]   Cache cleared for Sample.Repos.Sqlite

11:18:21.993 [debug] QUERY OK db=0.4ms decode=0.7ms idle=3.3ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:21.993 [info]   No tables found in Sample.Repos.Sqlite

11:18:21.993 [info]   Cache warmed up for 0 tables

11:18:21.995 [info] Successful: 1

11:18:21.995 [info] Failed: 0

11:18:22.841 [info] Refreshing Drops.Relation cache...

11:18:22.849 [info] Repositories: [Sample.Repos.Sqlite]

11:18:22.849 [info] Tables: all

11:18:22.849 [info] Processing repository: Sample.Repos.Sqlite

11:18:22.857 [info]   Cache cleared for Sample.Repos.Sqlite

11:18:22.864 [debug] QUERY OK db=0.6ms decode=0.7ms idle=4.7ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:22.864 [info]   No tables found in Sample.Repos.Sqlite

11:18:22.864 [info]   Cache warmed up for 0 tables

11:18:22.867 [info] Successful: 1

11:18:22.867 [info] Failed: 0

11:18:23.291 [info] Refreshing Drops.Relation cache...

11:18:23.298 [info] Repositories: [Sample.Repos.Sqlite]

11:18:23.302 [info] Tables: ["''"]

11:18:23.302 [info] Processing repository: Sample.Repos.Sqlite

11:18:23.309 [info]   Cache cleared for Sample.Repos.Sqlite

11:18:23.310 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.'' (not cached: {:file_read, :enoent})

11:18:23.316 [debug] QUERY OK db=0.4ms decode=0.7ms idle=8.7ms
PRAGMA foreign_key_list('') []

11:18:23.316 [debug] QUERY OK db=0.0ms idle=10.3ms
PRAGMA index_list('') []

11:18:23.316 [debug] QUERY OK db=0.0ms idle=10.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["''"]

11:18:23.316 [debug] QUERY OK db=0.0ms idle=10.4ms
PRAGMA table_info('') []

11:18:23.327 [info]   Cache warmed up for 1 tables

11:18:23.329 [info] Successful: 1

11:18:23.329 [info] Failed: 0
[36m
11:20:26.882 [debug] QUERY OK db=0.0ms idle=2.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []
[0m[36m
11:20:26.883 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})
[0m[36m
11:20:26.884 [debug] QUERY OK db=0.0ms idle=4.1ms
PRAGMA foreign_key_list(comments) []
[0m[36m
11:20:26.884 [debug] QUERY OK db=0.0ms idle=4.5ms
PRAGMA index_list(comments) []
[0m[36m
11:20:26.884 [debug] QUERY OK db=0.0ms idle=4.6ms
PRAGMA index_info(comments_approved_index) []
[0m[36m
11:20:26.884 [debug] QUERY OK db=0.0ms idle=4.6ms
PRAGMA index_info(comments_post_id_index) []
[0m[36m
11:20:26.884 [debug] QUERY OK db=0.0ms idle=4.6ms
PRAGMA index_info(comments_user_id_index) []
[0m[36m
11:20:26.885 [debug] QUERY OK db=0.0ms idle=4.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]
[0m[36m
11:20:26.885 [debug] QUERY OK db=0.1ms idle=5.5ms
PRAGMA table_info(comments) []
[0m[36m
11:20:26.900 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})
[0m[36m
11:20:26.901 [debug] QUERY OK db=0.0ms idle=21.1ms
PRAGMA foreign_key_list(posts) []
[0m[36m
11:20:26.901 [debug] QUERY OK db=0.1ms idle=21.1ms
PRAGMA index_list(posts) []
[0m[36m
11:20:26.901 [debug] QUERY OK db=0.0ms idle=19.5ms
PRAGMA index_info(posts_title_index) []
[0m[36m
11:20:26.901 [debug] QUERY OK db=0.0ms idle=17.4ms
PRAGMA index_info(posts_published_index) []
[0m[36m
11:20:26.901 [debug] QUERY OK db=0.0ms idle=17.0ms
PRAGMA index_info(posts_user_id_index) []
[0m[36m
11:20:26.901 [debug] QUERY OK db=0.0ms idle=17.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
[0m[36m
11:20:26.901 [debug] QUERY OK db=0.0ms idle=17.0ms
PRAGMA table_info(posts) []
[0m[36m
11:20:26.902 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})
[0m[36m
11:20:26.902 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA foreign_key_list(users) []
[0m[36m
11:20:26.902 [debug] QUERY OK db=0.0ms idle=17.7ms
PRAGMA index_list(users) []
[0m[36m
11:20:26.902 [debug] QUERY OK db=0.0ms idle=16.8ms
PRAGMA index_info(users_active_index) []
[0m[36m
11:20:26.902 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA index_info(users_email_index) []
[0m[36m
11:20:26.902 [debug] QUERY OK db=0.0ms idle=1.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
11:20:26.902 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA table_info(users) []
[0m[36m
11:21:58.297 [debug] QUERY OK db=0.0ms idle=8.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []
[0m[36m
11:21:58.298 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})
[0m[36m
11:21:58.298 [debug] QUERY OK db=0.0ms idle=9.4ms
PRAGMA foreign_key_list(comments) []
[0m[36m
11:21:58.298 [debug] QUERY OK db=0.0ms idle=9.5ms
PRAGMA index_list(comments) []
[0m[36m
11:21:58.298 [debug] QUERY OK db=0.0ms idle=9.1ms
PRAGMA index_info(comments_approved_index) []
[0m[36m
11:21:58.299 [debug] QUERY OK db=0.0ms idle=9.1ms
PRAGMA index_info(comments_post_id_index) []
[0m[36m
11:21:58.299 [debug] QUERY OK db=0.0ms idle=9.1ms
PRAGMA index_info(comments_user_id_index) []
[0m[36m
11:21:58.299 [debug] QUERY OK db=0.0ms idle=9.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]
[0m[36m
11:21:58.299 [debug] QUERY OK db=0.0ms idle=10.0ms
PRAGMA table_info(comments) []
[0m[36m
11:21:58.318 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=28.1ms
PRAGMA foreign_key_list(posts) []
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=28.1ms
PRAGMA index_list(posts) []
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=21.3ms
PRAGMA index_info(posts_title_index) []
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=19.3ms
PRAGMA index_info(posts_published_index) []
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=19.3ms
PRAGMA index_info(posts_user_id_index) []
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=19.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=19.3ms
PRAGMA table_info(posts) []
[0m[36m
11:21:58.318 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=19.8ms
PRAGMA foreign_key_list(users) []
[0m[36m
11:21:58.319 [debug] QUERY OK db=0.0ms idle=19.9ms
PRAGMA index_list(users) []
[0m[36m
11:21:58.319 [debug] QUERY OK db=0.0ms idle=19.0ms
PRAGMA index_info(users_active_index) []
[0m[36m
11:21:58.319 [debug] QUERY OK db=0.0ms idle=0.9ms
PRAGMA index_info(users_email_index) []
[0m[36m
11:21:58.319 [debug] QUERY OK db=0.0ms idle=0.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
11:21:58.319 [debug] QUERY OK db=0.0ms idle=0.9ms
PRAGMA table_info(users) []
[0m
11:23:17.573 [info] Refreshing Drops.Relation cache...

11:23:17.577 [info] Repositories: [Sample.Repos.Sqlite]

11:23:17.577 [info] Tables: ["''"]

11:23:17.577 [info] Processing repository: Sample.Repos.Sqlite

11:23:17.590 [info]   Cache cleared for Sample.Repos.Sqlite

11:23:17.590 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.'' (not cached: {:file_read, :enoent})

11:23:17.596 [debug] QUERY OK db=0.6ms decode=0.9ms idle=3.0ms
PRAGMA foreign_key_list('') []

11:23:17.596 [debug] QUERY OK db=0.0ms idle=4.7ms
PRAGMA index_list('') []

11:23:17.596 [debug] QUERY OK db=0.0ms idle=4.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["''"]

11:23:17.596 [debug] QUERY OK db=0.0ms idle=4.9ms
PRAGMA table_info('') []

11:23:17.606 [info]   Cache warmed up for 1 tables

11:23:17.606 [info] Successful: 1

11:23:17.606 [info] Failed: 0

11:23:19.108 [info] Refreshing Drops.Relation cache...

11:23:19.115 [info] Repositories: [Sample.Repos.Sqlite]

11:23:19.115 [info] Tables: all

11:23:19.115 [info] Processing repository: Sample.Repos.Sqlite

11:23:19.125 [info]   Cache cleared for Sample.Repos.Sqlite

11:23:19.133 [debug] QUERY OK db=0.6ms decode=1.2ms idle=4.6ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:19.135 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:23:19.135 [debug] QUERY OK db=0.1ms idle=8.6ms
PRAGMA foreign_key_list(comments) []

11:23:19.135 [debug] QUERY OK db=0.0ms idle=8.8ms
PRAGMA index_list(comments) []

11:23:19.135 [debug] QUERY OK db=0.1ms idle=8.8ms
PRAGMA index_info(comments_approved_index) []

11:23:19.135 [debug] QUERY OK db=0.1ms idle=8.9ms
PRAGMA index_info(comments_post_id_index) []

11:23:19.136 [debug] QUERY OK db=0.1ms idle=9.1ms
PRAGMA index_info(comments_user_id_index) []

11:23:19.137 [debug] QUERY OK db=0.0ms idle=9.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:23:19.137 [debug] QUERY OK db=0.0ms idle=10.4ms
PRAGMA table_info(comments) []

11:23:19.155 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:23:19.155 [debug] QUERY OK db=0.0ms idle=28.6ms
PRAGMA foreign_key_list(posts) []

11:23:19.155 [debug] QUERY OK db=0.0ms idle=28.7ms
PRAGMA index_list(posts) []

11:23:19.155 [debug] QUERY OK db=0.0ms idle=23.4ms
PRAGMA index_info(posts_title_index) []

11:23:19.155 [debug] QUERY OK db=0.0ms idle=20.1ms
PRAGMA index_info(posts_published_index) []

11:23:19.155 [debug] QUERY OK db=0.0ms idle=20.0ms
PRAGMA index_info(posts_user_id_index) []

11:23:19.155 [debug] QUERY OK db=0.0ms idle=19.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:23:19.155 [debug] QUERY OK db=0.0ms idle=19.8ms
PRAGMA table_info(posts) []

11:23:19.156 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:23:19.156 [debug] QUERY OK db=0.0ms idle=20.4ms
PRAGMA foreign_key_list(users) []

11:23:19.156 [debug] QUERY OK db=0.0ms idle=20.4ms
PRAGMA index_list(users) []

11:23:19.156 [debug] QUERY OK db=0.0ms idle=19.3ms
PRAGMA index_info(users_active_index) []

11:23:19.156 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA index_info(users_email_index) []

11:23:19.156 [debug] QUERY OK db=0.0ms idle=1.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:19.156 [debug] QUERY OK db=0.0ms idle=1.1ms
PRAGMA table_info(users) []

11:23:19.157 [info]   Cache warmed up for 3 tables

11:23:19.159 [info] Successful: 1

11:23:19.159 [info] Failed: 0

11:23:20.012 [info] Refreshing Drops.Relation cache...

11:23:20.019 [info] Repositories: [Sample.Repos.Sqlite]

11:23:20.022 [info] Tables: ["non_existent_table"]

11:23:20.022 [info] Processing repository: Sample.Repos.Sqlite

11:23:20.025 [info]   Cache cleared for Sample.Repos.Sqlite

11:23:20.028 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.non_existent_table (not cached: {:file_read, :enoent})

11:23:20.034 [debug] QUERY OK db=0.5ms decode=0.9ms idle=5.0ms
PRAGMA foreign_key_list(non_existent_table) []

11:23:20.034 [debug] QUERY OK db=0.1ms idle=7.2ms
PRAGMA index_list(non_existent_table) []

11:23:20.035 [debug] QUERY OK db=0.0ms idle=7.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["non_existent_table"]

11:23:20.035 [debug] QUERY OK db=0.0ms idle=7.5ms
PRAGMA table_info(non_existent_table) []

11:23:20.049 [info]   Cache warmed up for 1 tables

11:23:20.049 [info] Successful: 1

11:23:20.049 [info] Failed: 0

11:23:20.759 [info] Refreshing Drops.Relation cache...

11:23:20.763 [info] Repositories: [Sample.Repos.Sqlite]

11:23:20.763 [info] Tables: all

11:23:20.763 [info] Processing repository: Sample.Repos.Sqlite

11:23:20.764 [info]   Cache cleared for Sample.Repos.Sqlite

11:23:20.774 [debug] QUERY OK db=0.5ms decode=0.8ms idle=2.3ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:20.775 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:23:20.775 [debug] QUERY OK db=0.0ms idle=4.4ms
PRAGMA foreign_key_list(comments) []

11:23:20.775 [debug] QUERY OK db=0.0ms idle=4.4ms
PRAGMA index_list(comments) []

11:23:20.775 [debug] QUERY OK db=0.0ms idle=4.5ms
PRAGMA index_info(comments_approved_index) []

11:23:20.775 [debug] QUERY OK db=0.0ms idle=4.5ms
PRAGMA index_info(comments_post_id_index) []

11:23:20.775 [debug] QUERY OK db=0.0ms idle=4.6ms
PRAGMA index_info(comments_user_id_index) []

11:23:20.776 [debug] QUERY OK db=0.0ms idle=4.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:23:20.776 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA table_info(comments) []

11:23:20.792 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:23:20.792 [debug] QUERY OK db=0.0ms queue=0.1ms idle=21.8ms
PRAGMA foreign_key_list(posts) []

11:23:20.792 [debug] QUERY OK db=0.0ms idle=22.1ms
PRAGMA index_list(posts) []

11:23:20.792 [debug] QUERY OK db=0.0ms idle=19.3ms
PRAGMA index_info(posts_title_index) []

11:23:20.793 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA index_info(posts_published_index) []

11:23:20.793 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA index_info(posts_user_id_index) []

11:23:20.793 [debug] QUERY OK db=0.0ms idle=17.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:23:20.793 [debug] QUERY OK db=0.0ms idle=17.9ms
PRAGMA table_info(posts) []

11:23:20.794 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:23:20.794 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA foreign_key_list(users) []

11:23:20.794 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA index_list(users) []

11:23:20.794 [debug] QUERY OK db=0.0ms idle=17.9ms
PRAGMA index_info(users_active_index) []

11:23:20.794 [debug] QUERY OK db=0.0ms idle=1.8ms
PRAGMA index_info(users_email_index) []

11:23:20.794 [debug] QUERY OK db=0.0ms idle=1.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:20.794 [debug] QUERY OK db=0.0ms idle=1.7ms
PRAGMA table_info(users) []

11:23:20.796 [info]   Cache warmed up for 3 tables

11:23:20.796 [info] Successful: 1

11:23:20.796 [info] Failed: 0

11:23:21.258 [debug] QUERY OK db=0.5ms decode=0.7ms idle=2.1ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:21.261 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:23:21.261 [debug] QUERY OK db=0.1ms idle=9.2ms
PRAGMA foreign_key_list(comments) []

11:23:21.261 [debug] QUERY OK db=0.0ms idle=9.5ms
PRAGMA index_list(comments) []

11:23:21.262 [debug] QUERY OK db=0.1ms idle=9.5ms
PRAGMA index_info(comments_approved_index) []

11:23:21.262 [debug] QUERY OK db=0.0ms idle=9.8ms
PRAGMA index_info(comments_post_id_index) []

11:23:21.262 [debug] QUERY OK db=0.0ms idle=9.8ms
PRAGMA index_info(comments_user_id_index) []

11:23:21.263 [debug] QUERY OK db=0.0ms idle=9.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:23:21.263 [debug] QUERY OK db=0.0ms idle=10.8ms
PRAGMA table_info(comments) []

11:23:21.278 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:23:21.278 [debug] QUERY OK db=0.0ms idle=26.4ms
PRAGMA foreign_key_list(posts) []

11:23:21.279 [debug] QUERY OK db=0.0ms idle=26.5ms
PRAGMA index_list(posts) []

11:23:21.279 [debug] QUERY OK db=0.0ms idle=24.0ms
PRAGMA index_info(posts_title_index) []

11:23:21.279 [debug] QUERY OK db=0.0ms idle=17.2ms
PRAGMA index_info(posts_published_index) []

11:23:21.279 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA index_info(posts_user_id_index) []

11:23:21.279 [debug] QUERY OK db=0.0ms idle=17.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:23:21.279 [debug] QUERY OK db=0.0ms idle=17.0ms
PRAGMA table_info(posts) []

11:23:21.280 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:23:21.280 [debug] QUERY OK db=0.0ms idle=18.2ms
PRAGMA foreign_key_list(users) []

11:23:21.280 [debug] QUERY OK db=0.0ms idle=18.3ms
PRAGMA index_list(users) []

11:23:21.280 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA index_info(users_active_index) []

11:23:21.280 [debug] QUERY OK db=0.0ms idle=1.8ms
PRAGMA index_info(users_email_index) []

11:23:21.280 [debug] QUERY OK db=0.0ms idle=1.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:21.280 [debug] QUERY OK db=0.0ms idle=1.8ms
PRAGMA table_info(users) []

11:23:22.144 [info] Refreshing Drops.Relation cache...

11:23:22.148 [info] Repositories: [Sample.Repos.Sqlite]

11:23:22.148 [info] Tables: all

11:23:22.149 [info] Processing repository: Sample.Repos.Sqlite

11:23:22.150 [info]   Cache cleared for Sample.Repos.Sqlite

11:23:22.158 [debug] QUERY OK db=0.4ms decode=0.7ms idle=3.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:22.159 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:23:22.159 [debug] QUERY OK db=0.0ms idle=5.1ms
PRAGMA foreign_key_list(comments) []

11:23:22.159 [debug] QUERY OK db=0.0ms idle=5.2ms
PRAGMA index_list(comments) []

11:23:22.159 [debug] QUERY OK db=0.0ms idle=5.2ms
PRAGMA index_info(comments_approved_index) []

11:23:22.159 [debug] QUERY OK db=0.0ms idle=5.3ms
PRAGMA index_info(comments_post_id_index) []

11:23:22.160 [debug] QUERY OK db=0.0ms idle=5.3ms
PRAGMA index_info(comments_user_id_index) []

11:23:22.160 [debug] QUERY OK db=0.0ms idle=5.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:23:22.161 [debug] QUERY OK db=0.0ms idle=6.4ms
PRAGMA table_info(comments) []

11:23:22.176 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:23:22.176 [debug] QUERY OK db=0.0ms idle=22.1ms
PRAGMA foreign_key_list(posts) []

11:23:22.176 [debug] QUERY OK db=0.0ms idle=22.2ms
PRAGMA index_list(posts) []

11:23:22.176 [debug] QUERY OK db=0.0ms idle=19.6ms
PRAGMA index_info(posts_title_index) []

11:23:22.176 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA index_info(posts_published_index) []

11:23:22.177 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA index_info(posts_user_id_index) []

11:23:22.177 [debug] QUERY OK db=0.0ms idle=17.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:23:22.177 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA table_info(posts) []

11:23:22.177 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:23:22.177 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA foreign_key_list(users) []

11:23:22.177 [debug] QUERY OK db=0.0ms idle=17.7ms
PRAGMA index_list(users) []

11:23:22.178 [debug] QUERY OK db=0.0ms idle=16.9ms
PRAGMA index_info(users_active_index) []

11:23:22.178 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA index_info(users_email_index) []

11:23:22.178 [debug] QUERY OK db=0.0ms idle=1.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:22.178 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA table_info(users) []

11:23:22.179 [info]   Cache warmed up for 3 tables

11:23:22.179 [info] Successful: 1

11:23:22.179 [info] Failed: 0

11:23:22.670 [info] Refreshing Drops.Relation cache...

11:23:22.676 [info] Repositories: [Sample.Repos.Sqlite]

11:23:22.676 [info] Tables: all

11:23:22.676 [info] Processing repository: Sample.Repos.Sqlite

11:23:22.685 [info]   Cache cleared for Sample.Repos.Sqlite

11:23:22.690 [debug] QUERY OK db=0.5ms decode=0.7ms idle=2.8ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:22.691 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:23:22.691 [debug] QUERY OK db=0.0ms idle=5.6ms
PRAGMA foreign_key_list(comments) []

11:23:22.691 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA index_list(comments) []

11:23:22.691 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA index_info(comments_approved_index) []

11:23:22.691 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA index_info(comments_post_id_index) []

11:23:22.691 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA index_info(comments_user_id_index) []

11:23:22.692 [debug] QUERY OK db=0.0ms idle=5.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:23:22.692 [debug] QUERY OK db=0.0ms idle=6.7ms
PRAGMA table_info(comments) []

11:23:22.707 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:23:22.707 [debug] QUERY OK db=0.0ms idle=22.1ms
PRAGMA foreign_key_list(posts) []

11:23:22.708 [debug] QUERY OK db=0.0ms idle=22.2ms
PRAGMA index_list(posts) []

11:23:22.708 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA index_info(posts_title_index) []

11:23:22.708 [debug] QUERY OK db=0.0ms idle=16.6ms
PRAGMA index_info(posts_published_index) []

11:23:22.708 [debug] QUERY OK db=0.0ms idle=16.6ms
PRAGMA index_info(posts_user_id_index) []

11:23:22.708 [debug] QUERY OK db=0.0ms idle=16.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:23:22.708 [debug] QUERY OK db=0.0ms idle=16.7ms
PRAGMA table_info(posts) []

11:23:22.708 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:23:22.708 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA foreign_key_list(users) []

11:23:22.709 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA index_list(users) []

11:23:22.709 [debug] QUERY OK db=0.0ms idle=16.5ms
PRAGMA index_info(users_active_index) []

11:23:22.709 [debug] QUERY OK db=0.0ms idle=1.1ms
PRAGMA index_info(users_email_index) []

11:23:22.709 [debug] QUERY OK db=0.0ms idle=1.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:22.709 [debug] QUERY OK db=0.0ms idle=1.0ms
PRAGMA table_info(users) []

11:23:22.710 [info]   Cache warmed up for 3 tables

11:23:22.711 [info] Successful: 1

11:23:22.711 [info] Failed: 0

11:23:23.152 [info] Refreshing Drops.Relation cache...

11:23:23.160 [info] Repositories: [Sample.Repos.Sqlite]

11:23:23.162 [info] Tables: ["users", "posts"]

11:23:23.162 [info] Processing repository: Sample.Repos.Sqlite

11:23:23.171 [info]   Cache cleared for Sample.Repos.Sqlite

11:23:23.172 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:23:23.177 [debug] QUERY OK db=0.4ms decode=0.6ms idle=9.1ms
PRAGMA foreign_key_list(users) []

11:23:23.177 [debug] QUERY OK db=0.0ms idle=10.7ms
PRAGMA index_list(users) []

11:23:23.177 [debug] QUERY OK db=0.0ms idle=10.8ms
PRAGMA index_info(users_active_index) []

11:23:23.177 [debug] QUERY OK db=0.0ms idle=10.9ms
PRAGMA index_info(users_email_index) []

11:23:23.177 [debug] QUERY OK db=0.0ms idle=11.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:23.177 [debug] QUERY OK db=0.0ms idle=11.0ms
PRAGMA table_info(users) []

11:23:23.194 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:23:23.194 [debug] QUERY OK db=0.0ms idle=28.2ms
PRAGMA foreign_key_list(posts) []

11:23:23.194 [debug] QUERY OK db=0.0ms idle=28.3ms
PRAGMA index_list(posts) []

11:23:23.194 [debug] QUERY OK db=0.0ms idle=28.3ms
PRAGMA index_info(posts_title_index) []

11:23:23.194 [debug] QUERY OK db=0.0ms idle=28.4ms
PRAGMA index_info(posts_published_index) []

11:23:23.194 [debug] QUERY OK db=0.0ms idle=18.8ms
PRAGMA index_info(posts_user_id_index) []

11:23:23.195 [debug] QUERY OK db=0.0ms idle=17.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:23:23.195 [debug] QUERY OK db=0.0ms idle=17.7ms
PRAGMA table_info(posts) []

11:23:23.200 [info]   Cache warmed up for 2 tables

11:23:23.202 [info] Successful: 1

11:23:23.202 [info] Failed: 0

11:23:52.724 [debug] QUERY OK db=0.6ms decode=1.0ms idle=45.0ms
PRAGMA foreign_key_list(users) []

11:23:52.725 [debug] QUERY OK db=0.0ms idle=49.1ms
PRAGMA index_list(users) []

11:23:52.726 [debug] QUERY OK db=0.0ms idle=49.2ms
PRAGMA index_info(users_active_index) []

11:23:52.726 [debug] QUERY OK db=0.0ms idle=49.4ms
PRAGMA index_info(users_email_index) []

11:23:52.727 [debug] QUERY OK db=0.1ms idle=49.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:52.727 [debug] QUERY OK db=0.0ms idle=50.7ms
PRAGMA table_info(users) []

11:23:53.254 [debug] QUERY OK db=0.4ms decode=0.7ms queue=2.3ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:53.262 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:23:53.262 [debug] QUERY OK db=0.0ms idle=10.3ms
PRAGMA foreign_key_list(comments) []

11:23:53.262 [debug] QUERY OK db=0.0ms idle=10.3ms
PRAGMA index_list(comments) []

11:23:53.262 [debug] QUERY OK db=0.0ms idle=10.4ms
PRAGMA index_info(comments_approved_index) []

11:23:53.262 [debug] QUERY OK db=0.0ms idle=10.5ms
PRAGMA index_info(comments_post_id_index) []

11:23:53.262 [debug] QUERY OK db=0.0ms idle=10.6ms
PRAGMA index_info(comments_user_id_index) []

11:23:53.263 [debug] QUERY OK db=0.0ms idle=10.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:23:53.263 [debug] QUERY OK db=0.0ms idle=11.4ms
PRAGMA table_info(comments) []

11:23:54.459 [debug] QUERY OK db=0.7ms decode=0.8ms queue=3.3ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:55.691 [debug] QUERY OK db=0.8ms decode=0.8ms queue=3.2ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:56.493 [debug] QUERY OK db=0.9ms decode=0.9ms idle=44.9ms
PRAGMA foreign_key_list(users) []

11:23:56.494 [debug] QUERY OK db=0.1ms idle=49.3ms
PRAGMA index_list(users) []

11:23:56.494 [debug] QUERY OK db=0.0ms idle=49.4ms
PRAGMA index_info(users_active_index) []

11:23:56.494 [debug] QUERY OK db=0.1ms idle=47.2ms
PRAGMA index_info(users_email_index) []

11:23:56.496 [debug] QUERY OK db=0.0ms idle=47.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:56.496 [debug] QUERY OK db=0.0ms idle=48.6ms
PRAGMA table_info(users) []

11:23:57.036 [debug] QUERY OK db=0.9ms decode=0.8ms queue=2.7ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []
[36m
11:31:21.890 [debug] QUERY OK db=0.0ms idle=7.4ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []
[0m[36m
11:31:21.891 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})
[0m[36m
11:31:21.892 [debug] QUERY OK db=0.0ms idle=8.9ms
PRAGMA foreign_key_list(comments) []
[0m[36m
11:31:21.892 [debug] QUERY OK db=0.0ms idle=9.0ms
PRAGMA index_list(comments) []
[0m[36m
11:31:21.892 [debug] QUERY OK db=0.0ms idle=9.0ms
PRAGMA index_info(comments_approved_index) []
[0m[36m
11:31:21.892 [debug] QUERY OK db=0.0ms idle=8.6ms
PRAGMA index_info(comments_post_id_index) []
[0m[36m
11:31:21.892 [debug] QUERY OK db=0.0ms idle=8.7ms
PRAGMA index_info(comments_user_id_index) []
[0m[36m
11:31:21.893 [debug] QUERY OK db=0.0ms idle=8.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]
[0m[36m
11:31:21.893 [debug] QUERY OK db=0.0ms idle=9.8ms
PRAGMA table_info(comments) []
[0m[36m
11:31:21.913 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})
[0m[36m
11:31:21.913 [debug] QUERY OK db=0.0ms idle=29.9ms
PRAGMA foreign_key_list(posts) []
[0m[36m
11:31:21.913 [debug] QUERY OK db=0.0ms idle=29.9ms
PRAGMA index_list(posts) []
[0m[36m
11:31:21.913 [debug] QUERY OK db=0.0ms idle=23.6ms
PRAGMA index_info(posts_title_index) []
[0m[36m
11:31:21.913 [debug] QUERY OK db=0.0ms idle=21.5ms
PRAGMA index_info(posts_published_index) []
[0m[36m
11:31:21.913 [debug] QUERY OK db=0.0ms idle=21.5ms
PRAGMA index_info(posts_user_id_index) []
[0m[36m
11:31:21.913 [debug] QUERY OK db=0.0ms idle=21.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
[0m[36m
11:31:21.913 [debug] QUERY OK db=0.0ms idle=21.6ms
PRAGMA table_info(posts) []
[0m[36m
11:31:21.914 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})
[0m[36m
11:31:21.914 [debug] QUERY OK db=0.0ms idle=22.2ms
PRAGMA foreign_key_list(users) []
[0m[36m
11:31:21.914 [debug] QUERY OK db=0.0ms idle=22.2ms
PRAGMA index_list(users) []
[0m[36m
11:31:21.914 [debug] QUERY OK db=0.0ms idle=21.0ms
PRAGMA index_info(users_active_index) []
[0m[36m
11:31:21.914 [debug] QUERY OK db=0.0ms idle=1.0ms
PRAGMA index_info(users_email_index) []
[0m[36m
11:31:21.914 [debug] QUERY OK db=0.0ms idle=1.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
11:31:21.914 [debug] QUERY OK db=0.0ms idle=1.0ms
PRAGMA table_info(users) []
[0m[36m
11:31:22.417 [debug] QUERY OK db=0.6ms decode=0.7ms idle=1.9ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []
[0m[36m
11:31:22.420 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})
[0m[36m
11:31:22.420 [debug] QUERY OK db=0.1ms idle=9.3ms
PRAGMA foreign_key_list(comments) []
[0m[36m
11:31:22.420 [debug] QUERY OK db=0.0ms idle=9.5ms
PRAGMA index_list(comments) []
[0m[36m
11:31:22.420 [debug] QUERY OK db=0.0ms idle=9.6ms
PRAGMA index_info(comments_approved_index) []
[0m[36m
11:31:22.420 [debug] QUERY OK db=0.0ms idle=9.7ms
PRAGMA index_info(comments_post_id_index) []
[0m[36m
11:31:22.420 [debug] QUERY OK db=0.0ms idle=9.8ms
PRAGMA index_info(comments_user_id_index) []
[0m[36m
11:31:22.421 [debug] QUERY OK db=0.0ms idle=9.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]
[0m[36m
11:31:22.421 [debug] QUERY OK db=0.1ms idle=10.7ms
PRAGMA table_info(comments) []
[0m[36m
11:31:22.439 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})
[0m[36m
11:31:22.439 [debug] QUERY OK db=0.0ms idle=28.5ms
PRAGMA foreign_key_list(posts) []
[0m[36m
11:31:22.439 [debug] QUERY OK db=0.0ms idle=28.6ms
PRAGMA index_list(posts) []
[0m[36m
11:31:22.439 [debug] QUERY OK db=0.0ms idle=26.1ms
PRAGMA index_info(posts_title_index) []
[0m[36m
11:31:22.439 [debug] QUERY OK db=0.0ms idle=19.2ms
PRAGMA index_info(posts_published_index) []
[0m[36m
11:31:22.439 [debug] QUERY OK db=0.0ms idle=19.2ms
PRAGMA index_info(posts_user_id_index) []
[0m[36m
11:31:22.439 [debug] QUERY OK db=0.0ms idle=19.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
[0m[36m
11:31:22.439 [debug] QUERY OK db=0.0ms idle=19.2ms
PRAGMA table_info(posts) []
[0m[36m
11:31:22.440 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})
[0m[36m
11:31:22.440 [debug] QUERY OK db=0.0ms idle=19.8ms
PRAGMA foreign_key_list(users) []
[0m[36m
11:31:22.440 [debug] QUERY OK db=0.0ms idle=19.8ms
PRAGMA index_list(users) []
[0m[36m
11:31:22.440 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA index_info(users_active_index) []
[0m[36m
11:31:22.440 [debug] QUERY OK db=0.0ms idle=1.1ms
PRAGMA index_info(users_email_index) []
[0m[36m
11:31:22.440 [debug] QUERY OK db=0.0ms idle=1.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
11:31:22.440 [debug] QUERY OK db=0.0ms idle=1.1ms
PRAGMA table_info(users) []
[0m